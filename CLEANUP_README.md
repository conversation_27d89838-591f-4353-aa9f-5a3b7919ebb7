# JoMaDe Project Cleanup

This document provides information about the cleanup performed on the JoMaDe project and how to regenerate the removed directories when needed.

## Directories Removed

The following directories were removed during cleanup:

1. `node_modules/` - Node.js dependencies
2. `frontend/.next/` - Next.js build output
3. `backend/.venv/` - Backend Python virtual environment
4. `venv/` - Root Python virtual environment
5. `.cursor/` - Cursor editor files

## How to Regenerate

### Node.js Dependencies

To reinstall Node.js dependencies:

```bash
npm install
```

### Next.js Build

To rebuild the Next.js application:

```bash
cd frontend
npm install
npm run build
```

### Python Virtual Environments

To recreate the backend virtual environment:

```bash
cd backend
python -m venv .venv
# On Windows:
.venv\Scripts\activate
# On Unix/MacOS:
source .venv/bin/activate
pip install -r requirements.txt
```

To recreate the root virtual environment:

```bash
python -m venv venv
# On Windows:
venv\Scripts\activate
# On Unix/MacOS:
source venv/bin/activate
pip install -r requirements.txt
```

## Using the Run Script

Alternatively, you can use the provided run script which will automatically set up all necessary environments:

```bash
# For Windows Command Prompt:
run_jomade.bat

# For PowerShell:
.\run_jomade.ps1

# For Python:
python run_jomade.py
```

The run script will:
- Check if all required tools are installed
- Set up the backend virtual environment and install dependencies
- Set up the frontend and install dependencies
- Run both the backend and frontend servers
- Open the application in your default browser
