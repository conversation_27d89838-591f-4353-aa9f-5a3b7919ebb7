"""
Simplified API for JoMaDe application.
This replaces the complex API structure with a simpler, flatter approach.
"""

from fastapi import FastAP<PERSON>, HTTPException, Body, Query
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any, Optional
import logging
import os
from datetime import datetime

# Import our simplified storage
from storage import JobUrlStore, CVStore, JobStore
from scraper import create_scraper

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create the FastAPI app
app = FastAPI(
    title="JoMaDe API",
    description="Job Market Detector API - Simplified Version",
    version="0.2.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize data stores
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
os.makedirs(DATA_DIR, exist_ok=True)

job_urls_store = JobUrlStore(os.path.join(DATA_DIR, "job_urls.json"))
cv_store = CVStore(os.path.join(DATA_DIR, "cv.json"))
jobs_store = JobStore(os.path.join(DATA_DIR, "jobs.json"))

# Migrate data from existing markdown files if available
def migrate_from_markdown():
    """Migrate data from markdown files to JSON if needed."""
    try:
        # Check for job_url.md
        job_url_md_path = os.path.join(os.path.dirname(__file__), "job_url.md")
        if os.path.exists(job_url_md_path) and len(job_urls_store.get_all()) == 0:
            logger.info(f"Migrating data from {job_url_md_path}")
            with open(job_url_md_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Skip comment lines (first 10 lines)
            for i, line in enumerate(lines[10:], start=0):
                url = line.strip()
                if url and not url.startswith('#'):
                    job_urls_store.add({"url": url})

            logger.info(f"Migrated {len(job_urls_store.get_all())} URLs from markdown")

        # Check for CV_Summary.md
        cv_md_path = os.path.join(os.path.dirname(__file__), "CV_Summary.md")
        if os.path.exists(cv_md_path) and len(cv_store.get_all()) == 0:
            logger.info(f"Migrating data from {cv_md_path}")
            with open(cv_md_path, 'r', encoding='utf-8') as f:
                content = f.read()

            cv_store.add({"summary": content})
            logger.info("Migrated CV summary from markdown")

    except Exception as e:
        logger.error(f"Error migrating data: {str(e)}")

# Try to migrate data on startup
migrate_from_markdown()

# API Routes

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to JoMaDe API - Simplified Version",
        "version": "0.2.0",
        "docs_url": "/docs"
    }

# Job URLs endpoints
@app.get("/api/urls")
async def get_urls():
    """Get all job URLs"""
    return job_urls_store.get_all()

@app.post("/api/urls")
async def add_url(url_data: Dict[str, Any] = Body(...)):
    """Add a new job URL"""
    if "url" not in url_data:
        raise HTTPException(status_code=400, detail="URL is required")

    return job_urls_store.add(url_data)

@app.delete("/api/urls/{url_id}")
async def delete_url(url_id: str):
    """Delete a job URL"""
    if job_urls_store.delete(url_id):
        return {"message": f"URL with ID {url_id} deleted successfully"}
    raise HTTPException(status_code=404, detail=f"URL with ID {url_id} not found")

@app.put("/api/urls/{url_id}")
async def update_url(url_id: str, url_data: Dict[str, Any] = Body(...)):
    """Update a job URL"""
    result = job_urls_store.update(url_id, url_data)
    if result:
        return result
    raise HTTPException(status_code=404, detail=f"URL with ID {url_id} not found")

# CV endpoints
@app.get("/api/cv")
async def get_cv():
    """Get CV data"""
    cv_data = cv_store.get_all()
    if cv_data:
        return cv_data[0]
    return {"summary": ""}

@app.put("/api/cv")
async def update_cv(cv_data: Dict[str, Any] = Body(...)):
    """Update CV data"""
    if "summary" not in cv_data:
        raise HTTPException(status_code=400, detail="Summary is required")

    cv_entries = cv_store.get_all()
    if cv_entries:
        return cv_store.update(cv_entries[0]["id"], cv_data)
    return cv_store.add(cv_data)

# Jobs endpoints
@app.get("/api/jobs")
async def get_jobs(source: Optional[str] = Query(None), shortlisted: Optional[bool] = Query(None)):
    """
    Get jobs with optional filtering

    Args:
        source: Filter by source prefix (e.g., AAA)
        shortlisted: Filter by shortlisted status
    """
    if source:
        return jobs_store.get_by_source(source)
    if shortlisted is not None:
        if shortlisted:
            return jobs_store.get_shortlisted()
        return [job for job in jobs_store.get_all() if not job.get('isShortlisted', False)]
    return jobs_store.get_all()

@app.post("/api/jobs")
async def add_job(job_data: Dict[str, Any] = Body(...)):
    """Add a new job"""
    return jobs_store.add(job_data)

@app.put("/api/jobs/{job_id}")
async def update_job(job_id: str, job_data: Dict[str, Any] = Body(...)):
    """Update a job"""
    result = jobs_store.update(job_id, job_data)
    if result:
        return result
    raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")

@app.delete("/api/jobs/{job_id}")
async def delete_job(job_id: str):
    """Delete a job"""
    if jobs_store.delete(job_id):
        return {"message": f"Job with ID {job_id} deleted successfully"}
    raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")

# Scraping endpoint
@app.post("/api/scrape")
async def scrape_jobs():
    """
    Trigger job scraping using Firecrawl API
    """
    try:
        # Get URLs from storage
        urls = job_urls_store.get_all()

        if not urls:
            return {
                "success": False,
                "message": "No URLs configured for scraping",
                "url_count": 0,
                "job_count": 0
            }

        # Create scraper instance
        print(f"🚀 Starting scraping process for {len(urls)} URLs...")
        scraper = create_scraper()
        if not scraper:
            # Fallback to mock data if scraper can't be created (missing API keys)
            print("⚠️  Scraper not available, using mock data")
            logger.warning("Scraper not available, using mock data")
            return await _mock_scrape_jobs(urls)

        # Prepare URLs with prefixes for scraping
        urls_with_prefixes = [
            {"url": url.get("url", ""), "prefix": url.get("prefix", "AAA")}
            for url in urls
            if url.get("url")
        ]

        # Perform actual scraping
        scrape_result = scraper.scrape_multiple_urls(urls_with_prefixes)

        # Store scraped jobs
        for job in scrape_result.get("jobs", []):
            jobs_store.add(job)

        return {
            "success": scrape_result["success"],
            "timestamp": scrape_result["timestamp"],
            "url_count": scrape_result["url_count"],
            "successful_urls": scrape_result["successful_urls"],
            "failed_urls": scrape_result["failed_urls"],
            "job_count": scrape_result["job_count"],
            "message": scrape_result["message"]
        }

    except Exception as e:
        logger.error(f"Error in scrape_jobs: {str(e)}")
        return {
            "success": False,
            "message": f"Scraping failed: {str(e)}",
            "url_count": len(job_urls_store.get_all()),
            "job_count": 0
        }


async def _mock_scrape_jobs(urls):
    """Fallback mock scraping when Firecrawl is not available"""
    result = {
        "success": True,
        "timestamp": datetime.now().isoformat(),
        "url_count": len(urls),
        "job_count": len(urls) * 3,  # Mock: 3 jobs per URL
        "message": "Mock jobs scraped (Firecrawl not configured)"
    }

    # Add some mock jobs
    for url in urls:
        prefix = url.get("prefix", "AAA")
        for i in range(3):  # 3 mock jobs per URL
            job_id = f"{prefix}{i+1:02d}"
            jobs_store.add({
                "id": job_id,
                "title": f"Mock Job {job_id}",
                "company": "Example Company",
                "location": "Remote",
                "description": f"This is a mock job for {url.get('url')}",
                "source": prefix,
                "link": url.get("url"),
                "isShortlisted": False
            })

    return result

# Simple frontend endpoints (matching the HTML frontend expectations)
@app.get("/job-urls")
async def get_job_urls_simple():
    """Get job URLs in simple format for HTML frontend"""
    urls = job_urls_store.get_all()
    return {"urls": [url.get("url", "") for url in urls]}

@app.post("/job-urls")
async def save_job_urls_simple(data: Dict[str, List[str]] = Body(...)):
    """Save job URLs from HTML frontend"""
    urls = data.get("urls", [])

    # Clear existing URLs
    for url_entry in job_urls_store.get_all():
        job_urls_store.delete(url_entry["id"])

    # Add new URLs
    for i, url in enumerate(urls):
        if url.strip():
            prefix = f"{'A' * (i // 26 + 1)}{chr(65 + (i % 26))}{chr(65 + (i % 26))}"
            job_urls_store.add({"url": url.strip(), "prefix": prefix})

    return {"message": f"Saved {len(urls)} URLs successfully"}

@app.get("/cv-summary")
async def get_cv_summary_simple():
    """Get CV summary for HTML frontend"""
    cv_data = cv_store.get_all()
    if cv_data:
        return {"summary": cv_data[0].get("summary", "")}
    return {"summary": ""}

@app.post("/cv-summary")
async def save_cv_summary_simple(data: Dict[str, str] = Body(...)):
    """Save CV summary from HTML frontend"""
    summary = data.get("summary", "")

    cv_entries = cv_store.get_all()
    if cv_entries:
        cv_store.update(cv_entries[0]["id"], {"summary": summary})
    else:
        cv_store.add({"summary": summary})

    return {"message": "CV summary saved successfully"}

@app.post("/scrape-jobs")
async def scrape_jobs_simple():
    """Scrape jobs endpoint for HTML frontend"""
    return await scrape_jobs()

@app.get("/jobs")
async def get_jobs_simple():
    """Get jobs in simple format for HTML frontend"""
    jobs = jobs_store.get_all()
    return {"jobs": jobs}

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "api_version": "0.2.0",
        "storage": {
            "job_urls": len(job_urls_store.get_all()),
            "cv": len(cv_store.get_all()),
            "jobs": len(jobs_store.get_all())
        }
    }

# Test endpoint to debug URL loading
@app.get("/test/urls")
async def test_urls():
    """Test endpoint to check URL loading"""
    urls = job_urls_store.get_all()
    return {
        "count": len(urls),
        "urls": urls[:3],  # Show first 3 URLs for testing
        "message": f"Found {len(urls)} URLs in storage"
    }

# Run the application with: uvicorn api:app --reload
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api:app", host="0.0.0.0", port=8000, reload=True)
