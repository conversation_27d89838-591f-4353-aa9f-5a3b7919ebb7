"""
Simple JSON-based file storage for JoMaDe application.
This replaces the complex markdown-based storage with a simpler JSON approach.
"""

import json
import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JsonStore:
    """A simple JSON file-based data store."""
    
    def __init__(self, filename: str):
        """
        Initialize the JSON store.
        
        Args:
            filename: Path to the JSON file
        """
        self.filename = filename
        self.data = []
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        # Load data from file or create empty file
        self.load()
    
    def load(self) -> None:
        """Load data from the JSON file."""
        try:
            if os.path.exists(self.filename):
                with open(self.filename, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                logger.info(f"Loaded {len(self.data)} items from {self.filename}")
            else:
                self.data = []
                self.save()  # Create the file
                logger.info(f"Created new data file: {self.filename}")
        except Exception as e:
            logger.error(f"Error loading data from {self.filename}: {str(e)}")
            self.data = []
            self.save()  # Create the file with empty data
    
    def save(self) -> None:
        """Save data to the JSON file."""
        try:
            with open(self.filename, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, indent=2)
            logger.info(f"Saved {len(self.data)} items to {self.filename}")
        except Exception as e:
            logger.error(f"Error saving data to {self.filename}: {str(e)}")
    
    def get_all(self) -> List[Dict[str, Any]]:
        """Get all items from the store."""
        return self.data
    
    def get_by_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """Get an item by its ID."""
        for item in self.data:
            if item.get('id') == item_id:
                return item
        return None
    
    def add(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new item to the store.
        
        Args:
            item: The item to add
            
        Returns:
            The added item with generated ID if not provided
        """
        # Generate ID if not provided
        if 'id' not in item:
            item['id'] = str(len(self.data) + 1)
        
        # Add timestamps
        now = datetime.now().isoformat()
        item['created_at'] = now
        item['updated_at'] = now
        
        self.data.append(item)
        self.save()
        return item
    
    def update(self, item_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Update an existing item.
        
        Args:
            item_id: ID of the item to update
            updates: Dictionary of fields to update
            
        Returns:
            The updated item or None if not found
        """
        for i, item in enumerate(self.data):
            if item.get('id') == item_id:
                # Update the item
                self.data[i].update(updates)
                # Update timestamp
                self.data[i]['updated_at'] = datetime.now().isoformat()
                self.save()
                return self.data[i]
        return None
    
    def delete(self, item_id: str) -> bool:
        """
        Delete an item by ID.
        
        Args:
            item_id: ID of the item to delete
            
        Returns:
            True if deleted, False if not found
        """
        for i, item in enumerate(self.data):
            if item.get('id') == item_id:
                self.data.pop(i)
                self.save()
                return True
        return False
    
    def clear(self) -> None:
        """Clear all data from the store."""
        self.data = []
        self.save()


# Specialized stores for specific data types

class JobUrlStore(JsonStore):
    """Store for job URLs with additional functionality."""
    
    def add(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Add a job URL with additional fields."""
        # Generate prefix (AAA, AAB, etc.)
        prefix = self._generate_prefix(len(self.data))
        
        # Set defaults
        item.setdefault('is_active', True)
        item.setdefault('prefix', prefix)
        item.setdefault('name', f"Job Source {len(self.data) + 1}")
        
        return super().add(item)
    
    def _generate_prefix(self, index: int) -> str:
        """Generate a three-letter prefix (AAA, AAB, AAC, etc.) from an index."""
        # Ensure index is positive
        index = max(0, index)
        
        # Convert to base-26 (A-Z) with 3 digits
        first_char = chr(65 + (index // 676) % 26)  # 26^2 = 676
        second_char = chr(65 + (index // 26) % 26)
        third_char = chr(65 + index % 26)
        
        return f"{first_char}{second_char}{third_char}"


class CVStore(JsonStore):
    """Store for CV data with additional functionality."""
    
    def get_summary(self) -> str:
        """Get the CV summary text."""
        if not self.data:
            return ""
        return self.data[0].get('summary', '')
    
    def update_summary(self, summary: str) -> Dict[str, Any]:
        """Update the CV summary."""
        if not self.data:
            # Create new CV entry
            return self.add({'summary': summary})
        
        # Update existing entry
        return self.update(self.data[0]['id'], {'summary': summary})


class JobStore(JsonStore):
    """Store for job data with additional functionality."""
    
    def get_by_source(self, source_prefix: str) -> List[Dict[str, Any]]:
        """Get jobs by source prefix."""
        return [job for job in self.data if job.get('source') == source_prefix]
    
    def get_shortlisted(self) -> List[Dict[str, Any]]:
        """Get shortlisted jobs."""
        return [job for job in self.data if job.get('isShortlisted', False)]
