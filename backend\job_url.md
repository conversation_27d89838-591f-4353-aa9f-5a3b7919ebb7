# Job URLs for Scraping
# 
# Each URL in this file will be assigned a unique three-letter prefix (AAA, AAB, AAC, etc.)
# when scraped. Jobs from each source will be numbered sequentially (AAA1, AAA2, AAA3, etc.)
# This creates a unique ID for each job that's used throughout the application.
#
# The prefix system supports up to 17,576 different job sources (AAA through ZZZ).
#
# Format: One URL per line, no comments or empty lines between URLs

https://careers.eoexecutives.com/
https://mbmanagement.de/category/aktuelle-stellenangebote
https://vakanzen.drmaier-partner.de/stellenanzeigen/
https://www.baumann-ag.com/fuer-kandidaten/bewerbungsverfahren/vakanzen/joblist
https://www.hapeko.de/bewerbende/stellenangebote/p1
https://jobs.senator-partners.de/
https://www.converitas.de/fuer-kandidaten/stellenangebote/
https://troeger-cie.de/offene-stellen/
https://careers.vattenfall.com/global/en/search-results
https://www.robertwalters.de/jobs.html
https://ifp-online.de/executive-positions/
https://heiden-associates.com/stellenangebote/
https://www.humancouncil.com/de/jobs-aktuelle-mandate-auswahl
https://career.etec-consult.de/stellenanzeigen/
https://www.mentis-consulting.de/aktueller-stellenmarkt/
https://zeppelin.wd3.myworkdayjobs.com/de-DE/careers
