"""
Simple Firecrawl-based job scraper for JoMaDe application.
This module handles scraping job URLs using Firecrawl API and extracting job information using OpenAI.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import re

from firecrawl import FirecrawlApp
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JobScraper:
    """Simple job scraper using Firecrawl and OpenAI."""

    def __init__(self):
        """Initialize the scraper with API keys from environment."""
        self.firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')

        if not self.firecrawl_api_key:
            raise ValueError("FIRECRAWL_API_KEY not found in environment variables")
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")

        # Initialize Firecrawl
        self.firecrawl = FirecrawlApp(api_key=self.firecrawl_api_key)

        # Initialize OpenAI
        openai.api_key = self.openai_api_key
        self.openai_client = openai.OpenAI(api_key=self.openai_api_key)

        logger.info("JobScraper initialized successfully")

    def crawl_url(self, url: str, source_prefix: str) -> List[Dict[str, Any]]:
        """
        Crawl a website for job listings using Firecrawl.

        Args:
            url: The URL to crawl
            source_prefix: The three-letter prefix for this source (e.g., AAA)

        Returns:
            List of job dictionaries
        """
        try:
            print(f"🕷️ CRAWLING: {url} (prefix: {source_prefix})")
            logger.info(f"Starting crawl for URL: {url} with prefix: {source_prefix}")

            # Crawl the website using Firecrawl
            print(f"📡 Calling Firecrawl CRAWL API for {url}...")
            from firecrawl import ScrapeOptions

            crawl_result = self.firecrawl.crawl_url(
                url,
                limit=50,  # Limit to 50 pages to avoid excessive usage
                scrape_options=ScrapeOptions(
                    formats=['markdown'],
                    only_main_content=True
                )
            )

            if not crawl_result or not crawl_result.get('data'):
                print(f"❌ No content crawled from {url}")
                logger.warning(f"No content crawled from {url}")
                return []

            pages_data = crawl_result['data']
            print(f"✅ Crawled {len(pages_data)} pages from {url}")
            logger.info(f"Crawled {len(pages_data)} pages from {url}")

            # Process all crawled pages to extract jobs
            all_jobs = []
            for i, page in enumerate(pages_data):
                if 'markdown' in page:
                    print(f"🔍 Processing page {i+1}/{len(pages_data)}: {page.get('metadata', {}).get('sourceURL', 'Unknown')}")

                    # Extract job information using OpenAI
                    jobs = self._extract_jobs_with_llm(
                        page['markdown'],
                        page.get('metadata', {}).get('sourceURL', url),
                        source_prefix,
                        page_number=i+1
                    )

                    if jobs:
                        all_jobs.extend(jobs)
                        print(f"  ✅ Found {len(jobs)} jobs on this page")

            print(f"🎯 TOTAL: Extracted {len(all_jobs)} jobs from {url}")
            logger.info(f"Successfully extracted {len(all_jobs)} jobs from {url}")
            return all_jobs

        except Exception as e:
            print(f"❌ ERROR crawling {url}: {str(e)}")
            logger.error(f"Error crawling {url}: {str(e)}")
            return []

    def _extract_jobs_with_llm(self, content: str, source_url: str, source_prefix: str, page_number: int = 1) -> List[Dict[str, Any]]:
        """
        Extract job information from scraped content using OpenAI.

        Args:
            content: The scraped markdown content
            source_url: The original URL
            source_prefix: The three-letter prefix for this source

        Returns:
            List of job dictionaries
        """
        try:
            # Improved prompt for job extraction
            prompt = f"""
            You are a job listing extraction expert. Extract ALL job postings from the following content.

            Look for:
            - Job titles/positions
            - Company names
            - Job locations
            - Job descriptions or summaries
            - Application links or job detail URLs

            For each job found, return a JSON object with:
            - title: The job title/position name
            - company: Company name (extract from content or use "Company not specified")
            - location: Job location (city, country, or "Remote" if specified, or "Location not specified")
            - description: Brief job description or summary (max 200 characters)
            - link: Direct job application URL if found, otherwise use the source URL: {source_url}

            Return ONLY a valid JSON array of job objects. If no jobs are found, return [].

            Content to analyze:
            {content[:6000]}
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",  # Better model for extraction
                messages=[
                    {"role": "system", "content": "You are a job listing extraction expert. You MUST return valid JSON array format. Extract ALL job postings from the content, even if information is incomplete."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,  # More tokens for better extraction
                temperature=0.0   # Deterministic output
            )

            # Parse the response
            response_text = response.choices[0].message.content.strip()
            print(f"🤖 LLM Response length: {len(response_text)} characters")

            # Try to extract JSON from the response
            import json
            import re

            jobs_data = []
            try:
                # First try: direct JSON parsing
                jobs_data = json.loads(response_text)
                print(f"✅ Direct JSON parsing successful: {len(jobs_data)} jobs")
            except json.JSONDecodeError:
                try:
                    # Second try: extract JSON array from response
                    json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                    if json_match:
                        jobs_data = json.loads(json_match.group())
                        print(f"✅ Regex JSON extraction successful: {len(jobs_data)} jobs")
                    else:
                        print("❌ No JSON array found in LLM response")
                        print(f"Response preview: {response_text[:500]}...")
                        return []
                except json.JSONDecodeError as e:
                    print(f"❌ JSON parsing failed: {str(e)}")
                    print(f"Response preview: {response_text[:500]}...")
                    return []

            # Process and format the jobs
            jobs = []
            for i, job_data in enumerate(jobs_data[:20]):  # Limit to 20 jobs per page
                job_id = f"{source_prefix}{page_number:02d}{i+1:02d}"  # e.g., AAA0101, AAA0102

                job = {
                    "id": job_id,
                    "title": job_data.get("title", "Unknown Position"),
                    "company": job_data.get("company", "Unknown Company"),
                    "location": job_data.get("location", "Location not specified"),
                    "description": job_data.get("description", "No description available")[:200],
                    "source": source_prefix,
                    "link": job_data.get("link", source_url),
                    "isShortlisted": False,
                    "scraped_at": datetime.now().isoformat()
                }
                jobs.append(job)

            return jobs

        except Exception as e:
            logger.error(f"Error extracting jobs with LLM: {str(e)}")
            return []

    def scrape_multiple_urls(self, urls_with_prefixes: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Scrape multiple URLs for job listings.

        Args:
            urls_with_prefixes: List of dicts with 'url' and 'prefix' keys

        Returns:
            Dictionary with scraping results
        """
        print(f"\n🚀 STARTING BATCH SCRAPING: {len(urls_with_prefixes)} URLs")
        logger.info(f"Starting batch scraping of {len(urls_with_prefixes)} URLs")

        all_jobs = []
        successful_urls = 0
        failed_urls = []

        for i, url_data in enumerate(urls_with_prefixes, 1):
            url = url_data.get('url', '')
            prefix = url_data.get('prefix', 'AAA')

            if not url:
                print(f"⚠️  Skipping empty URL at position {i}")
                continue

            print(f"\n📋 Processing {i}/{len(urls_with_prefixes)}: {prefix}")
            jobs = self.crawl_url(url, prefix)
            if jobs:
                all_jobs.extend(jobs)
                successful_urls += 1
                print(f"✅ Success: {len(jobs)} jobs added (Total: {len(all_jobs)})")
            else:
                failed_urls.append(url)
                print(f"❌ Failed: No jobs found")

        print(f"\n🎯 SCRAPING COMPLETE:")
        print(f"   📊 Total Jobs: {len(all_jobs)}")
        print(f"   ✅ Successful URLs: {successful_urls}/{len(urls_with_prefixes)}")
        print(f"   ❌ Failed URLs: {len(failed_urls)}")

        result = {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "url_count": len(urls_with_prefixes),
            "successful_urls": successful_urls,
            "failed_urls": failed_urls,
            "job_count": len(all_jobs),
            "jobs": all_jobs,
            "message": f"Scraped {len(all_jobs)} jobs from {successful_urls}/{len(urls_with_prefixes)} URLs"
        }

        logger.info(f"Batch scraping completed: {len(all_jobs)} jobs from {successful_urls}/{len(urls_with_prefixes)} URLs")
        return result


def create_scraper() -> Optional[JobScraper]:
    """
    Factory function to create a JobScraper instance.
    Returns None if API keys are not configured.
    """
    try:
        print("🔧 Initializing Firecrawl scraper...")
        scraper = JobScraper()
        print("✅ Firecrawl scraper initialized successfully!")
        return scraper
    except ValueError as e:
        print(f"❌ Failed to create scraper: {str(e)}")
        print("🔄 Will use mock scraping instead")
        logger.error(f"Failed to create scraper: {str(e)}")
        return None
