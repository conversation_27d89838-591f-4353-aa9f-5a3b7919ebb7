"""
Simple Firecrawl-based job scraper for JoMaDe application.
This module handles scraping job URLs using Firecrawl API and extracting job information using OpenAI.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import re

from firecrawl import FirecrawlApp
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JobScraper:
    """Simple job scraper using Firecrawl and OpenAI."""
    
    def __init__(self):
        """Initialize the scraper with API keys from environment."""
        self.firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        if not self.firecrawl_api_key:
            raise ValueError("FIRECRAWL_API_KEY not found in environment variables")
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")
        
        # Initialize Firecrawl
        self.firecrawl = FirecrawlApp(api_key=self.firecrawl_api_key)
        
        # Initialize OpenAI
        openai.api_key = self.openai_api_key
        self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        
        logger.info("JobScraper initialized successfully")
    
    def scrape_url(self, url: str, source_prefix: str) -> List[Dict[str, Any]]:
        """
        Scrape a single URL for job listings.
        
        Args:
            url: The URL to scrape
            source_prefix: The three-letter prefix for this source (e.g., AAA)
            
        Returns:
            List of job dictionaries
        """
        try:
            logger.info(f"Scraping URL: {url} with prefix: {source_prefix}")
            
            # Scrape the URL using Firecrawl
            scrape_result = self.firecrawl.scrape_url(
                url, 
                formats=['markdown', 'html']
            )
            
            if not scrape_result or 'markdown' not in scrape_result:
                logger.warning(f"No content scraped from {url}")
                return []
            
            markdown_content = scrape_result['markdown']
            
            # Extract job information using OpenAI
            jobs = self._extract_jobs_with_llm(markdown_content, url, source_prefix)
            
            logger.info(f"Extracted {len(jobs)} jobs from {url}")
            return jobs
            
        except Exception as e:
            logger.error(f"Error scraping {url}: {str(e)}")
            return []
    
    def _extract_jobs_with_llm(self, content: str, source_url: str, source_prefix: str) -> List[Dict[str, Any]]:
        """
        Extract job information from scraped content using OpenAI.
        
        Args:
            content: The scraped markdown content
            source_url: The original URL
            source_prefix: The three-letter prefix for this source
            
        Returns:
            List of job dictionaries
        """
        try:
            # Simple prompt for job extraction
            prompt = f"""
            Extract job listings from the following content. For each job, provide:
            - title: Job title
            - company: Company name (if available)
            - location: Job location (if available)
            - description: Brief job description (first 200 characters)
            - link: Direct link to the job (if available, otherwise use the source URL)
            
            Return the results as a JSON array. If no jobs are found, return an empty array.
            
            Content:
            {content[:4000]}  # Limit content to avoid token limits
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a job listing extraction assistant. Extract job information and return it as valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.1
            )
            
            # Parse the response
            response_text = response.choices[0].message.content.strip()
            
            # Try to extract JSON from the response
            import json
            try:
                # Look for JSON array in the response
                json_start = response_text.find('[')
                json_end = response_text.rfind(']') + 1
                if json_start >= 0 and json_end > json_start:
                    jobs_data = json.loads(response_text[json_start:json_end])
                else:
                    logger.warning("No JSON array found in LLM response")
                    return []
            except json.JSONDecodeError:
                logger.warning("Failed to parse JSON from LLM response")
                return []
            
            # Process and format the jobs
            jobs = []
            for i, job_data in enumerate(jobs_data[:10]):  # Limit to 10 jobs per source
                job_id = f"{source_prefix}{i+1:02d}"  # e.g., AAA01, AAA02
                
                job = {
                    "id": job_id,
                    "title": job_data.get("title", "Unknown Position"),
                    "company": job_data.get("company", "Unknown Company"),
                    "location": job_data.get("location", "Location not specified"),
                    "description": job_data.get("description", "No description available")[:200],
                    "source": source_prefix,
                    "link": job_data.get("link", source_url),
                    "isShortlisted": False,
                    "scraped_at": datetime.now().isoformat()
                }
                jobs.append(job)
            
            return jobs
            
        except Exception as e:
            logger.error(f"Error extracting jobs with LLM: {str(e)}")
            return []
    
    def scrape_multiple_urls(self, urls_with_prefixes: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Scrape multiple URLs for job listings.
        
        Args:
            urls_with_prefixes: List of dicts with 'url' and 'prefix' keys
            
        Returns:
            Dictionary with scraping results
        """
        all_jobs = []
        successful_urls = 0
        failed_urls = []
        
        for url_data in urls_with_prefixes:
            url = url_data.get('url', '')
            prefix = url_data.get('prefix', 'AAA')
            
            if not url:
                continue
                
            jobs = self.scrape_url(url, prefix)
            if jobs:
                all_jobs.extend(jobs)
                successful_urls += 1
            else:
                failed_urls.append(url)
        
        result = {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "url_count": len(urls_with_prefixes),
            "successful_urls": successful_urls,
            "failed_urls": failed_urls,
            "job_count": len(all_jobs),
            "jobs": all_jobs,
            "message": f"Scraped {len(all_jobs)} jobs from {successful_urls}/{len(urls_with_prefixes)} URLs"
        }
        
        return result


def create_scraper() -> Optional[JobScraper]:
    """
    Factory function to create a JobScraper instance.
    Returns None if API keys are not configured.
    """
    try:
        return JobScraper()
    except ValueError as e:
        logger.error(f"Failed to create scraper: {str(e)}")
        return None
