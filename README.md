# JoMaDe - Job Market Detector

JoMaDe is a web-based application for job market analysis and matching. It helps users find relevant job postings based on their CV and skills.

## Features

- Job scraping from various sources
- CV summary import and editing
- File upload for CV and documents
- Embedding files for RAG-based shortlisting
- Shortlisting jobs based on CV match
- Evaluating shortlisted jobs
- Kadoa integration for job scraping (optional)

## Prerequisites

- Python 3.8 or higher
- Node.js 18 or higher
- npm 8 or higher

## Quick Start

### Option 1: Using the run scripts (recommended)

1. For Command Prompt users:
   ```
   run.bat
   ```

2. For PowerShell users:
   ```
   .\run.ps1
   ```

   Note: If you encounter execution policy restrictions in PowerShell, you may need to run:
   ```
   Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
   .\run.ps1
   ```

The run scripts will:
- Check if all required tools are installed
- Set up the backend virtual environment and install dependencies
- Set up the frontend and install dependencies
- Run both the backend and frontend servers
- Open the application in your default browser

### Option 2: Manual setup

#### Backend Setup

1. Navigate to the backend directory:
   ```
   cd JoMaDe/backend
   ```

2. Create a virtual environment:
   ```
   python -m venv .venv
   ```

3. Activate the virtual environment:
   - Windows Command Prompt:
     ```
     .venv\Scripts\activate
     ```
   - Windows PowerShell:
     ```
     .venv\Scripts\Activate.ps1
     ```

4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

#### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd JoMaDe/frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

#### Running the Application Manually

1. Start the backend server:
   ```
   cd backend
   .venv\Scripts\python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. In a separate terminal, start the frontend server:
   ```
   cd frontend
   npx next dev
   ```

3. In a separate terminal, start the MCP server for console logs:
   ```
   python mcp_server.py
   ```

4. Open your browser and navigate to:
   - Frontend: http://localhost:3000
   - API Documentation: http://localhost:8000/docs

## Project Structure

- `backend/`: FastAPI backend application
  - `api/`: API endpoints
  - `core/`: Core functionality
  - `models/`: Database models
  - `services/`: Business logic
  - `utils/`: Utility functions

- `frontend/`: Next.js frontend application
  - `public/`: Static assets
  - `src/`: Source code
    - `components/`: React components
    - `pages/`: Next.js pages
    - `styles/`: CSS styles

- `shared/`: Shared code between frontend and backend
- `scripts/`: Utility scripts
- `docs/`: Documentation
- `tests/`: Test files

## Configuration

- Backend configuration is stored in `backend/.env`
- Frontend configuration is stored in `frontend/.env.local`
- Root configuration is stored in `.env`

## Development

- Backend API documentation is available at http://localhost:8000/docs
- Frontend application is available at http://localhost:3000

## API Endpoints

### Jobs

- `GET /api/v1/jobs`: Get all jobs
- `POST /api/v1/jobs/import`: Import jobs from sources
- `POST /api/v1/jobs/shortlist`: Shortlist jobs based on CV match
- `POST /api/v1/jobs/rag-shortlist`: Shortlist jobs using RAG
- `POST /api/v1/jobs/evaluate`: Evaluate shortlisted jobs

### CV

- `GET /api/v1/cv`: Get CV summary
- `POST /api/v1/cv`: Update CV summary

### Embeddings

- `GET /api/v1/embeddings/info`: Get information about current embedding
- `POST /api/v1/embeddings/create`: Create embedding from uploaded files
- `DELETE /api/v1/embeddings/delete`: Delete existing embedding

### Documents

- `POST /api/v1/documents/upload`: Upload documents
- `GET /api/v1/documents`: Get uploaded documents

### Job Sources

- `GET /api/v1/job-sources`: Get available job sources
- `POST /api/v1/job-sources`: Update job sources

### Kadoa

- `GET /api/v1/kadoa/workflows`: Get available Kadoa workflows
- `POST /api/v1/kadoa/run`: Run Kadoa workflow

## Workflow

1. Upload CV and other relevant documents
2. Create embeddings from uploaded documents
3. Import jobs from selected sources
4. Shortlist jobs using standard or RAG-based methods
5. Evaluate shortlisted jobs to get detailed match analysis